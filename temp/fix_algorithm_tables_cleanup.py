#!/usr/bin/env python3
"""
修复algorithm_results表清空时的外键约束错误
通过正确的顺序删除数据来避免DuckDB的内部错误
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from database.duckdb_manager import DuckDBManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_algorithm_tables_cleanup():
    """修复算法表清空问题 - 通过删除并重建表来解决DuckDB外键约束bug"""
    try:
        # 获取数据库路径
        from config.database_config import db_config
        db_path = db_config.get_main_db_path()

        logger.info(f"连接数据库: {db_path}")
        db_manager = DuckDBManager(db_path)

        # 获取所有表名
        tables_result = db_manager.execute_sql("""
            SELECT table_name as name, table_type
            FROM information_schema.tables
            WHERE table_schema = 'main'
        """)

        if not tables_result:
            logger.error("无法获取表列表")
            return False

        table_names = [t['name'] for t in tables_result]
        logger.info(f"找到 {len(table_names)} 个表")

        # 定义需要重建的算法相关表（从子表到父表的删除顺序）
        algorithm_tables_order = [
            # 第一层：详情表（引用其他表的表）
            'contract_risk_details',
            'same_account_wash_trading',
            'cross_account_wash_trading',
            'high_frequency_trading_details',
            'funding_rate_arbitrage_details',
            'wash_trading_pairs',

            # 第二层：中间表
            'wash_trading_results',

            # 第三层：主表
            'algorithm_results',

            # 其他相关表
            'position_analysis',
            'incomplete_positions_waiting'
        ]

        # 第一步：禁用外键约束检查并删除有问题的表
        logger.info("第一步：禁用外键约束检查并删除有问题的表...")
        dropped_tables = []

        try:
            # 禁用外键约束检查
            db_manager.execute_sql("PRAGMA foreign_keys = OFF")
            logger.info("已禁用外键约束检查")
        except Exception as e:
            logger.warning(f"禁用外键约束失败: {e}")

        for table_name in algorithm_tables_order:
            if table_name in table_names:
                try:
                    # 先检查表是否有数据
                    count_result = db_manager.execute_sql(f'SELECT COUNT(*) as count FROM {table_name}')
                    record_count = count_result[0]['count'] if count_result else 0

                    logger.info(f"删除表 {table_name} ({record_count} 条记录)...")
                    db_manager.execute_sql(f'DROP TABLE IF EXISTS {table_name}')
                    dropped_tables.append(table_name)
                    logger.info(f"✅ 已删除表: {table_name}")

                except Exception as e:
                    logger.error(f"❌ 删除表 {table_name} 失败: {e}")

        logger.info(f"已删除 {len(dropped_tables)} 个表")

        # 第二步：重建表结构
        logger.info("第二步：重建算法表结构...")

        # 使用算法存储管理器重建表
        from database.algorithm_storage_manager import AlgorithmStorageManager
        storage_manager = AlgorithmStorageManager(db_manager)

        # 初始化存储结构
        success = storage_manager.initialize_tables()
        if success:
            logger.info("✅ 算法表结构重建成功")

            # 重新启用外键约束检查
            try:
                db_manager.execute_sql("PRAGMA foreign_keys = ON")
                logger.info("已重新启用外键约束检查")
            except Exception as e:
                logger.warning(f"启用外键约束失败: {e}")
        else:
            logger.error("❌ 算法表结构重建失败")
            return False

        # 第三步：验证重建结果
        logger.info("第三步：验证重建结果...")

        # 重新获取表列表
        tables_result = db_manager.execute_sql("""
            SELECT table_name as name
            FROM information_schema.tables
            WHERE table_schema = 'main'
        """)

        current_table_names = [t['name'] for t in tables_result] if tables_result else []

        for table_name in dropped_tables:
            if table_name in current_table_names:
                try:
                    count_result = db_manager.execute_sql(f'SELECT COUNT(*) as count FROM {table_name}')
                    record_count = count_result[0]['count'] if count_result else 0
                    logger.info(f"✅ {table_name}: 重建成功 ({record_count} 条记录)")
                except Exception as e:
                    logger.error(f"❌ 验证表 {table_name} 失败: {e}")
            else:
                logger.warning(f"⚠️  表 {table_name} 未重建")

        logger.info("🎉 算法表清理修复完成！")
        return True

    except Exception as e:
        logger.error(f"修复过程失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 算法表清理修复工具")
    print("=" * 50)
    
    print("此工具将修复algorithm_results表清空时的外键约束错误")
    print("通过正确的顺序删除数据来避免DuckDB的内部错误")
    print()
    
    response = input("是否继续执行修复? (y/n): ").strip().lower()
    if response != 'y':
        print("操作已取消")
        return
    
    success = fix_algorithm_tables_cleanup()
    
    if success:
        print("\n✅ 修复成功！现在可以正常使用数据库清理功能了。")
    else:
        print("\n❌ 修复失败，请检查日志信息。")

if __name__ == "__main__":
    main()
