#!/usr/bin/env python3
"""
数据库重建解决方案
由于DuckDB外键约束的内部bug，我们需要重建整个数据库
"""

import sys
import os
import shutil
from datetime import datetime
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def backup_current_database():
    """备份当前数据库"""
    try:
        from config.database_config import db_config
        db_path = db_config.get_main_db_path()
        
        if not os.path.exists(db_path):
            logger.info("数据库文件不存在，无需备份")
            return True
        
        # 创建备份
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{db_path}.backup_{timestamp}"
        
        logger.info(f"备份数据库到: {backup_path}")
        shutil.copy2(db_path, backup_path)
        
        # 验证备份
        if os.path.exists(backup_path):
            backup_size = os.path.getsize(backup_path)
            original_size = os.path.getsize(db_path)
            
            if backup_size == original_size:
                logger.info(f"✅ 备份成功，文件大小: {backup_size} 字节")
                return backup_path
            else:
                logger.error(f"❌ 备份文件大小不匹配: {backup_size} vs {original_size}")
                return False
        else:
            logger.error("❌ 备份文件创建失败")
            return False
            
    except Exception as e:
        logger.error(f"备份失败: {e}")
        return False

def export_important_data():
    """导出重要数据"""
    try:
        from database.duckdb_manager import DuckDBManager
        
        logger.info("导出重要数据...")
        db_manager = DuckDBManager()
        
        # 导出用户认证数据
        auth_data = {}
        auth_tables = ['auth_users', 'auth_user_sessions', 'auth_user_activity_logs', 'auth_system_config']
        
        for table in auth_tables:
            try:
                data = db_manager.execute_sql(f'SELECT * FROM {table}')
                auth_data[table] = data
                logger.info(f"✅ 导出 {table}: {len(data) if data else 0} 条记录")
            except Exception as e:
                logger.warning(f"⚠️  导出 {table} 失败: {e}")
                auth_data[table] = []
        
        # 导出用户数据
        user_data = {}
        user_tables = ['users', 'user_relationships', 'user_trading_profiles']
        
        for table in user_tables:
            try:
                data = db_manager.execute_sql(f'SELECT * FROM {table}')
                user_data[table] = data
                logger.info(f"✅ 导出 {table}: {len(data) if data else 0} 条记录")
            except Exception as e:
                logger.warning(f"⚠️  导出 {table} 失败: {e}")
                user_data[table] = []
        
        # 导出代理分析数据
        agent_data = {}
        agent_tables = ['agent_analysis', 'shared_relationships', 'tasks']
        
        for table in agent_tables:
            try:
                data = db_manager.execute_sql(f'SELECT * FROM {table}')
                agent_data[table] = data
                logger.info(f"✅ 导出 {table}: {len(data) if data else 0} 条记录")
            except Exception as e:
                logger.warning(f"⚠️  导出 {table} 失败: {e}")
                agent_data[table] = []
        
        return {
            'auth_data': auth_data,
            'user_data': user_data,
            'agent_data': agent_data
        }
        
    except Exception as e:
        logger.error(f"导出数据失败: {e}")
        return None

def rebuild_database():
    """重建数据库"""
    try:
        from config.database_config import db_config
        db_path = db_config.get_main_db_path()
        
        logger.info("删除旧数据库文件...")
        if os.path.exists(db_path):
            os.remove(db_path)
            logger.info("✅ 旧数据库文件已删除")
        
        logger.info("创建新数据库...")
        from database.duckdb_manager import DuckDBManager
        
        # 创建新的数据库管理器实例
        db_manager = DuckDBManager(db_path)
        
        # 初始化基础表结构
        logger.info("初始化基础表结构...")
        
        # 1. 初始化认证表
        auth_schema_path = os.path.join(os.path.dirname(__file__), '..', 'backend', 'database', 'schema', 'auth_tables.sql')
        if os.path.exists(auth_schema_path):
            with open(auth_schema_path, 'r', encoding='utf-8') as f:
                auth_schema = f.read()
            db_manager.execute_script(auth_schema)
            logger.info("✅ 认证表结构创建成功")
        
        # 2. 初始化用户交易画像表
        profile_schema_path = os.path.join(os.path.dirname(__file__), '..', 'backend', 'database', 'schema', 'user_trading_profiles_unified.sql')
        if os.path.exists(profile_schema_path):
            with open(profile_schema_path, 'r', encoding='utf-8') as f:
                profile_schema = f.read()
            db_manager.execute_script(profile_schema)
            logger.info("✅ 用户交易画像表结构创建成功")
        
        # 3. 初始化算法表
        from database.algorithm_storage_manager import AlgorithmStorageManager
        storage_manager = AlgorithmStorageManager(db_manager)
        if storage_manager.initialize_tables():
            logger.info("✅ 算法表结构创建成功")
        else:
            logger.error("❌ 算法表结构创建失败")
            return False
        
        # 4. 初始化其他基础表
        db_manager.initialize_database()
        logger.info("✅ 基础表结构创建成功")
        
        return db_manager
        
    except Exception as e:
        logger.error(f"重建数据库失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def restore_important_data(db_manager, exported_data):
    """恢复重要数据"""
    try:
        logger.info("恢复重要数据...")
        
        if not exported_data:
            logger.warning("没有导出数据可恢复")
            return True
        
        # 恢复认证数据
        auth_data = exported_data.get('auth_data', {})
        for table, data in auth_data.items():
            if data:
                try:
                    # 获取表结构
                    schema = db_manager.execute_sql(f'DESCRIBE {table}')
                    columns = [col['column_name'] for col in schema]
                    
                    # 插入数据
                    for row in data:
                        values = [row.get(col) for col in columns]
                        placeholders = ', '.join(['?' for _ in columns])
                        sql = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({placeholders})"
                        db_manager.execute_sql(sql, values)
                    
                    logger.info(f"✅ 恢复 {table}: {len(data)} 条记录")
                except Exception as e:
                    logger.error(f"❌ 恢复 {table} 失败: {e}")
        
        # 恢复用户数据
        user_data = exported_data.get('user_data', {})
        for table, data in user_data.items():
            if data and table != 'user_trading_profiles':  # 跳过用户交易画像，因为结构可能已变化
                try:
                    # 获取表结构
                    schema = db_manager.execute_sql(f'DESCRIBE {table}')
                    columns = [col['column_name'] for col in schema]
                    
                    # 插入数据
                    for row in data:
                        values = [row.get(col) for col in columns]
                        placeholders = ', '.join(['?' for _ in columns])
                        sql = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({placeholders})"
                        db_manager.execute_sql(sql, values)
                    
                    logger.info(f"✅ 恢复 {table}: {len(data)} 条记录")
                except Exception as e:
                    logger.error(f"❌ 恢复 {table} 失败: {e}")
        
        # 恢复代理分析数据
        agent_data = exported_data.get('agent_data', {})
        for table, data in agent_data.items():
            if data:
                try:
                    # 获取表结构
                    schema = db_manager.execute_sql(f'DESCRIBE {table}')
                    columns = [col['column_name'] for col in schema]
                    
                    # 插入数据
                    for row in data:
                        values = [row.get(col) for col in columns]
                        placeholders = ', '.join(['?' for _ in columns])
                        sql = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({placeholders})"
                        db_manager.execute_sql(sql, values)
                    
                    logger.info(f"✅ 恢复 {table}: {len(data)} 条记录")
                except Exception as e:
                    logger.error(f"❌ 恢复 {table} 失败: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"恢复数据失败: {e}")
        return False

def test_new_database():
    """测试新数据库"""
    try:
        logger.info("测试新数据库...")
        from database.duckdb_manager import DuckDBManager
        
        db_manager = DuckDBManager()
        
        # 测试插入和删除algorithm_results
        test_data = {
            'id': 1,
            'task_id': 'test_task_001',
            'algorithm_type': 'test_algorithm'
        }
        
        # 插入测试数据
        db_manager.execute_sql(
            "INSERT INTO algorithm_results (id, task_id, algorithm_type) VALUES (?, ?, ?)",
            [test_data['id'], test_data['task_id'], test_data['algorithm_type']]
        )
        logger.info("✅ 测试数据插入成功")
        
        # 删除测试数据
        db_manager.execute_sql("DELETE FROM algorithm_results WHERE task_id = ?", [test_data['task_id']])
        logger.info("✅ 测试数据删除成功")
        
        logger.info("🎉 新数据库测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 新数据库测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 数据库重建解决方案")
    print("=" * 50)
    
    print("由于DuckDB外键约束的内部bug，我们需要重建整个数据库")
    print("⚠️  警告：这将删除当前数据库并创建新的数据库！")
    print("✅ 重要数据（用户认证、用户数据、代理分析）将被保留")
    print("❌ 算法分析结果数据将被清空")
    print()
    
    response = input("是否继续执行数据库重建? (y/n): ").strip().lower()
    if response != 'y':
        print("操作已取消")
        return
    
    # 步骤1：备份当前数据库
    print("\n步骤1：备份当前数据库...")
    backup_path = backup_current_database()
    if not backup_path:
        print("❌ 备份失败，操作终止")
        return
    
    # 步骤2：导出重要数据
    print("\n步骤2：导出重要数据...")
    exported_data = export_important_data()
    
    # 步骤3：重建数据库
    print("\n步骤3：重建数据库...")
    db_manager = rebuild_database()
    if not db_manager:
        print("❌ 数据库重建失败")
        return
    
    # 步骤4：恢复重要数据
    print("\n步骤4：恢复重要数据...")
    if not restore_important_data(db_manager, exported_data):
        print("⚠️  数据恢复部分失败，但数据库重建成功")
    
    # 步骤5：测试新数据库
    print("\n步骤5：测试新数据库...")
    if test_new_database():
        print("\n🎉 数据库重建成功！")
        print(f"📁 原数据库已备份到: {backup_path}")
        print("✅ 外键约束问题已解决，现在可以正常使用数据库清理功能")
    else:
        print("\n❌ 数据库重建失败")

if __name__ == "__main__":
    main()
