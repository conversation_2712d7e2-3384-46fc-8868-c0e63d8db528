#!/usr/bin/env python3
"""
最终修复algorithm_results表清空问题
通过删除并重建所有相关表来彻底解决DuckDB外键约束bug
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from database.duckdb_manager import DuckDBManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def final_fix_algorithm_tables():
    """最终修复算法表问题"""
    try:
        # 获取数据库路径
        from config.database_config import db_config
        db_path = db_config.get_main_db_path()
        
        logger.info(f"连接数据库: {db_path}")
        db_manager = DuckDBManager(db_path)
        
        # 获取所有表名
        tables_result = db_manager.execute_sql("""
            SELECT table_name as name
            FROM information_schema.tables
            WHERE table_schema = 'main'
        """)
        
        if not tables_result:
            logger.error("无法获取表列表")
            return False
        
        table_names = [t['name'] for t in tables_result]
        logger.info(f"找到 {len(table_names)} 个表")
        
        # 定义所有算法相关表（按依赖关系排序）
        algorithm_tables = [
            'contract_risk_details',
            'same_account_wash_trading', 
            'cross_account_wash_trading',
            'high_frequency_trading_details',
            'funding_rate_arbitrage_details',
            'wash_trading_pairs',
            'wash_trading_results',
            'algorithm_results',
            'position_analysis',
            'incomplete_positions_waiting'
        ]
        
        # 第一步：强制删除所有算法相关表
        logger.info("第一步：强制删除所有算法相关表...")
        dropped_tables = []
        
        for table_name in algorithm_tables:
            if table_name in table_names:
                try:
                    # 先检查表是否有数据
                    count_result = db_manager.execute_sql(f'SELECT COUNT(*) as count FROM {table_name}')
                    record_count = count_result[0]['count'] if count_result else 0
                    
                    logger.info(f"强制删除表 {table_name} ({record_count} 条记录)...")
                    
                    # 使用CASCADE删除（如果支持）
                    try:
                        db_manager.execute_sql(f'DROP TABLE {table_name} CASCADE')
                    except:
                        # 如果CASCADE不支持，直接删除
                        db_manager.execute_sql(f'DROP TABLE {table_name}')
                    
                    dropped_tables.append(table_name)
                    logger.info(f"✅ 已删除表: {table_name}")
                    
                except Exception as e:
                    logger.error(f"❌ 删除表 {table_name} 失败: {e}")
                    # 尝试使用IF EXISTS
                    try:
                        db_manager.execute_sql(f'DROP TABLE IF EXISTS {table_name}')
                        dropped_tables.append(table_name)
                        logger.info(f"✅ 已删除表 (IF EXISTS): {table_name}")
                    except Exception as e2:
                        logger.error(f"❌ 最终删除表 {table_name} 失败: {e2}")
        
        logger.info(f"已删除 {len(dropped_tables)} 个表")
        
        # 第二步：重建表结构
        logger.info("第二步：重建算法表结构...")
        
        # 使用算法存储管理器重建表
        from database.algorithm_storage_manager import AlgorithmStorageManager
        storage_manager = AlgorithmStorageManager(db_manager)
        
        # 初始化存储结构
        success = storage_manager.initialize_tables()
        if success:
            logger.info("✅ 算法表结构重建成功")
        else:
            logger.error("❌ 算法表结构重建失败")
            return False
        
        # 第三步：验证重建结果
        logger.info("第三步：验证重建结果...")
        
        # 重新获取表列表
        tables_result = db_manager.execute_sql("""
            SELECT table_name as name
            FROM information_schema.tables
            WHERE table_schema = 'main'
        """)
        
        current_table_names = [t['name'] for t in tables_result] if tables_result else []
        
        rebuilt_count = 0
        for table_name in dropped_tables:
            if table_name in current_table_names:
                try:
                    count_result = db_manager.execute_sql(f'SELECT COUNT(*) as count FROM {table_name}')
                    record_count = count_result[0]['count'] if count_result else 0
                    logger.info(f"✅ {table_name}: 重建成功 ({record_count} 条记录)")
                    rebuilt_count += 1
                except Exception as e:
                    logger.error(f"❌ 验证表 {table_name} 失败: {e}")
            else:
                logger.warning(f"⚠️  表 {table_name} 未重建")
        
        # 第四步：测试清空功能
        logger.info("第四步：测试清空功能...")
        
        try:
            # 插入测试数据
            test_data = {
                'task_id': 'test_task_001',
                'algorithm_type': 'test_algorithm',
                'contract_name': 'TEST_CONTRACT',
                'user_id': 'test_user',
                'risk_level': 'LOW',
                'confidence_score': 0.5,
                'trading_volume': 1000.0,
                'trading_frequency': 10
            }
            
            insert_sql = """
                INSERT INTO algorithm_results 
                (task_id, algorithm_type, contract_name, user_id, risk_level, confidence_score, trading_volume, trading_frequency)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            db_manager.execute_sql(insert_sql, list(test_data.values()))
            logger.info("✅ 测试数据插入成功")
            
            # 测试删除
            db_manager.execute_sql('DELETE FROM algorithm_results WHERE task_id = ?', ['test_task_001'])
            logger.info("✅ 测试数据删除成功")
            
            logger.info("🎉 清空功能测试通过！")
            
        except Exception as e:
            logger.error(f"❌ 清空功能测试失败: {e}")
            return False
        
        logger.info(f"🎉 算法表修复完成！重建了 {rebuilt_count} 个表")
        return True
        
    except Exception as e:
        logger.error(f"修复过程失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 算法表最终修复工具")
    print("=" * 50)
    
    print("此工具将彻底删除并重建所有算法相关表来解决外键约束问题")
    print("⚠️  警告：这将删除所有算法分析结果数据！")
    print()
    
    response = input("是否继续执行最终修复? (y/n): ").strip().lower()
    if response != 'y':
        print("操作已取消")
        return
    
    success = final_fix_algorithm_tables()
    
    if success:
        print("\n✅ 最终修复成功！现在可以正常使用数据库清理功能了。")
        print("📊 所有算法相关表已重建，外键约束问题已解决。")
    else:
        print("\n❌ 最终修复失败，请检查日志信息。")

if __name__ == "__main__":
    main()
