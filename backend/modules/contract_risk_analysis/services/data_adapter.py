#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合约数据适配器
负责新旧存储结构的切换和数据路由
"""

import logging
from typing import Optional, Dict, Any

import sys
import os

# 添加相对路径到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.append(backend_dir)

from database.duckdb_manager import DuckDBManager
from database.algorithm_storage_manager import AlgorithmStorageManager
from database.repositories.contract_risk_repository import ContractRiskRepository

logger = logging.getLogger(__name__)

class ContractDataAdapter:
    """合约数据适配器 - 管理新旧存储的切换"""

    _instance = None
    _initialized = False

    def __new__(cls, db_manager: Optional[DuckDBManager] = None):
        if cls._instance is None:
            cls._instance = super(ContractDataAdapter, cls).__new__(cls)
        return cls._instance

    def __init__(self, db_manager: Optional[DuckDBManager] = None):
        # 避免重复初始化
        if self._initialized:
            return

        self.db_manager = db_manager or DuckDBManager()
        self.storage_manager = AlgorithmStorageManager(self.db_manager)
        self.repository = ContractRiskRepository()
        # ========== 修改：默认启用新存储 ==========
        self._new_storage_enabled = True  # 强制启用新存储
        # 检查并初始化新存储
        if not self._check_new_storage_available():
            logger.warning("新存储表不存在，正在初始化...")
            self.storage_manager.initialize_tables()
        logger.debug("✅ 数据适配器已启用新存储模式")

        # 标记为已初始化
        ContractDataAdapter._initialized = True
        
    def _check_new_storage_available(self) -> bool:
        """检查新存储是否可用"""
        return self.storage_manager._check_new_storage_exists()
    
    def enable_new_storage(self) -> bool:
        """启用新存储结构"""
        try:
            if not self._check_new_storage_available():
                logger.warning("新存储结构不可用，请先初始化")
                return False
                
            self._new_storage_enabled = True
            logger.info("新存储结构已启用")
            return True
            
        except Exception as e:
            logger.error(f"启用新存储失败: {e}")
            return False
    
    def disable_new_storage(self) -> bool:
        """禁用新存储结构，回退到旧存储"""
        try:
            self._new_storage_enabled = False
            logger.info("已回退到旧存储结构")
            return True
            
        except Exception as e:
            logger.error(f"禁用新存储失败: {e}")
            return False
    
    def is_new_storage_enabled(self) -> bool:
        """检查新存储是否已启用"""
        return self._new_storage_enabled and self._check_new_storage_available()
    
    def save_analysis_result(self, task_id: str, analysis_type: str, filename: str,
                           total_contracts: int, risk_contracts: int,
                           wash_trading_count: int, cross_bd_count: int,
                           result_data: Dict[str, Any]) -> bool:
        """保存分析结果 - 智能路由"""
        try:
            # 优先使用新存储（旧存储表结构有问题）
            if self.is_new_storage_enabled():
                new_result_id = self.storage_manager.store_algorithm_result(
                    task_id=task_id,
                    algorithm_type=self._map_analysis_type(analysis_type),
                    result_data=result_data
                )

                if new_result_id:
                    logger.info(f"数据保存到新存储成功，result_id: {new_result_id}")
                    return True
                else:
                    logger.error("保存到新存储失败")

            # 如果新存储失败，尝试旧存储（但可能会失败）
            try:
                legacy_success = self.repository.save_analysis_result(
                    task_id, analysis_type, filename, total_contracts,
                    risk_contracts, wash_trading_count, cross_bd_count, result_data
                )

                if legacy_success:
                    logger.info("数据保存到旧存储成功")
                    return True
                else:
                    logger.error("保存到旧存储失败")

            except Exception as legacy_error:
                logger.warning(f"旧存储保存失败（表结构问题）: {legacy_error}")

            return False

        except Exception as e:
            logger.error(f"保存分析结果失败: {e}")
            return False
    
    def get_analysis_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取分析结果 - 智能查询"""
        try:
            # 如果启用了新存储，优先从新存储查询
            if self.is_new_storage_enabled():
                new_result = self.storage_manager.query_algorithm_results(task_id)
                # 检查新存储返回的数据是否完整
                if self._validate_new_storage_result(new_result):
                    logger.info(f"从新存储获取完整数据: {task_id}")
                    return self._format_for_compatibility(task_id, new_result)
                else:
                    logger.warning(f"新存储数据不完整（contract_risks为空），回退到老存储系统")
            
            # 回退到旧存储
            legacy_result = self.repository.get_analysis_result(task_id)
            if legacy_result:
                logger.info(f"从旧存储获取数据: {task_id}")
                
            return legacy_result
            
        except Exception as e:
            logger.error(f"获取分析结果失败: {e}")
            return None
    
    def _map_analysis_type(self, analysis_type: str) -> str:
        """映射分析类型到新存储的算法类型"""
        type_mapping = {
            'full': 'suspected_wash_trading',
            'wash_trading': 'suspected_wash_trading',
            'high_frequency': 'high_frequency_trading',
            'brush_trading': 'regular_brush_trading',
            'funding_arbitrage': 'funding_rate_arbitrage'
        }
        return type_mapping.get(analysis_type, 'suspected_wash_trading')
    
    def _validate_new_storage_result(self, result: Dict[str, Any]) -> bool:
        """验证新存储结果的完整性"""
        if not result:
            return False
        
        # 检查关键字段
        contract_risks = result.get('contract_risks', [])
        
        # 新存储必须有contract_risks且不为空，或者明确标记为无风险
        if isinstance(contract_risks, list):
            # 如果有风险事件，返回True
            if len(contract_risks) > 0:
                return True
            
            # 如果没有风险事件，检查是否有明确的统计信息表明确实没有风险
            summary = result.get('summary', {})
            if summary and (summary.get('total_analyzed', 0) > 0 or summary.get('risks_found', 0) == 0):
                return True
        
        # 其他情况认为数据不完整
        return False

    def _format_for_compatibility(self, task_id: str, new_result: Dict) -> Dict[str, Any]:
        """将新存储结果格式化为兼容的旧格式"""
        # 计算统计信息
        contract_risks = new_result.get('contract_risks', [])
        wash_trading_count = len([r for r in contract_risks 
                                if 'wash_trading' in r.get('detection_type', '')])
        cross_bd_count = len([r for r in contract_risks 
                            if 'cross_bd' in r.get('detection_method', '')])
        
        return {
            'task_id': task_id,
            'analysis_type': 'full',
            'result_data': new_result,
            'total_contracts': new_result.get('total_analyzed', len(contract_risks)),
            'risk_contracts': new_result.get('risks_found', len(contract_risks)),
            'wash_trading_count': wash_trading_count,
            'cross_bd_count': cross_bd_count,
            'created_at': new_result.get('created_at'),
            'updated_at': new_result.get('updated_at')
        }
    
    def get_storage_status(self) -> Dict[str, Any]:
        """获取存储状态信息"""
        return {
            'new_storage_available': self._check_new_storage_available(),
            'new_storage_enabled': self._new_storage_enabled,
            'current_mode': 'new_storage' if self.is_new_storage_enabled() else 'legacy_storage'
        } 