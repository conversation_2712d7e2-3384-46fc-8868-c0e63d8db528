"""
合约关联查询服务
负责查询指定用户及其下属关联账户的异常金额数据
"""

import logging
import tempfile
import os
from datetime import datetime
from typing import List, Dict, Any, Set
from database.duckdb_manager import db_manager

logger = logging.getLogger(__name__)

class ContractRelationQueryService:
    """合约关联查询服务类"""
    
    def __init__(self):
        self.logger = logger
        self.db_manager = db_manager
    
    def create_relation_query_task(self, user_id: str, include_direct_only: bool = True) -> Dict[str, Any]:
        """创建关联查询任务"""
        try:
            # 验证输入
            if not user_id:
                raise ValueError("user_id不能为空")

            self.logger.info(f"开始关联查询用户 {user_id} 及其下属账户的合约异常金额")

            # 1. 获取用户的下属关联账户
            related_users = self._get_related_users(user_id, include_direct_only)
            
            if not related_users:
                raise ValueError(f"未找到用户 {user_id} 的关联账户数据")

            # 包含原始用户ID
            all_user_ids = [user_id] + related_users
            self.logger.info(f"关联查询范围：目标用户1个，关联用户{len(related_users)}个，总计{len(all_user_ids)}个")

            # 2. 转换ID格式（如果需要）
            converted_ids = self._convert_ids_to_member_ids(all_user_ids)
            
            if not converted_ids:
                raise ValueError("没有找到有效的用户数据")

            self.logger.info(f"ID转换完成：输入{len(all_user_ids)}个ID，转换成功{len(converted_ids)}个member_id")

            # 3. 查询各类算法的异常金额
            results = self._query_abnormal_amounts(list(converted_ids.values()))

            # 4. 生成Excel文件（包含关联关系信息）
            file_path = self._generate_relation_excel_file(user_id, all_user_ids, results, converted_ids, related_users, include_direct_only)

            return {
                'status': 'success',
                'download_url': f'/api/contract/batch-query-download/{file_path.split("/")[-1]}',
                'file_path': file_path,
                'target_user': user_id,
                'related_users_count': len(related_users),
                'total_users': len(all_user_ids),
                'converted_users': len(converted_ids),
                'results_count': len(results),
                'query_mode': 'direct_only' if include_direct_only else 'full_tree'
            }

        except Exception as e:
            self.logger.error(f"关联查询失败: {str(e)}")
            raise

    def _get_related_users(self, user_id: str, include_direct_only: bool) -> List[str]:
        """获取用户的下属关联账户"""
        try:
            # 首先尝试通过BD金字塔获取关联用户
            related_users = self._get_related_users_from_pyramid(user_id, include_direct_only)
            
            if related_users:
                self.logger.info(f"通过BD金字塔找到 {len(related_users)} 个关联用户")
                return related_users
            
            # 如果BD金字塔没有数据，尝试通过用户关系表获取
            related_users = self._get_related_users_from_relationships(user_id, include_direct_only)
            
            if related_users:
                self.logger.info(f"通过用户关系表找到 {len(related_users)} 个关联用户")
                return related_users
            
            self.logger.warning(f"未找到用户 {user_id} 的任何关联账户")
            return []

        except Exception as e:
            self.logger.error(f"获取关联用户失败: {str(e)}")
            return []

    def _get_related_users_from_pyramid(self, user_id: str, include_direct_only: bool) -> List[str]:
        """从BD金字塔获取关联用户"""
        try:
            from database.repositories.user_repository import user_repository
            
            # 获取BD金字塔数据
            bd_pyramid_data = user_repository.get_bd_pyramid_data()
            
            if not bd_pyramid_data or not bd_pyramid_data.get('bd_trees'):
                return []
            
            # 在金字塔中查找目标用户
            target_node = self._find_user_in_pyramid(user_id, bd_pyramid_data['bd_trees'])
            
            if not target_node:
                self.logger.warning(f"在BD金字塔中未找到用户 {user_id}")
                return []
            
            # 获取下属用户
            related_users = []
            if include_direct_only:
                # 只获取直接下属
                related_users = self._get_direct_children(target_node)
            else:
                # 获取整个下属树
                related_users = self._get_all_descendants(target_node)
            
            return related_users

        except Exception as e:
            self.logger.error(f"从BD金字塔获取关联用户失败: {str(e)}")
            return []

    def _get_related_users_from_relationships(self, user_id: str, include_direct_only: bool) -> List[str]:
        """从用户关系表获取关联用户"""
        try:
            # 首先将user_id转换为digital_id（如果需要）
            digital_id = self._convert_to_digital_id(user_id)
            
            if not digital_id:
                return []
            
            if include_direct_only:
                # 只查询直接下级
                sql = """
                SELECT DISTINCT child_digital_id 
                FROM user_relationships 
                WHERE parent_digital_id = ?
                """
                params = [digital_id]
            else:
                # 递归查询所有下级（使用CTE）
                sql = """
                WITH RECURSIVE descendants AS (
                    SELECT child_digital_id, parent_digital_id, 1 as level
                    FROM user_relationships 
                    WHERE parent_digital_id = ?
                    
                    UNION ALL
                    
                    SELECT ur.child_digital_id, ur.parent_digital_id, d.level + 1
                    FROM user_relationships ur
                    INNER JOIN descendants d ON ur.parent_digital_id = d.child_digital_id
                    WHERE d.level < 10  -- 防止无限递归
                )
                SELECT DISTINCT child_digital_id FROM descendants
                """
                params = [digital_id]
            
            results = self.db_manager.execute_sql(sql, params)
            
            # 转换回member_id格式
            related_users = []
            for row in results:
                child_digital_id = row['child_digital_id']
                # 尝试转换为member_id
                member_id = self._convert_digital_to_member_id(child_digital_id)
                if member_id:
                    related_users.append(member_id)
            
            return related_users

        except Exception as e:
            self.logger.error(f"从用户关系表获取关联用户失败: {str(e)}")
            return []

    def _find_user_in_pyramid(self, user_id: str, bd_trees: List[Dict]) -> Dict:
        """在BD金字塔中查找指定用户"""
        def search_in_tree(node: Dict, target_id: str) -> Dict:
            user_info = node.get('user_info', {})
            
            # 检查多种ID格式
            if (str(user_info.get('member_id', '')) == target_id or
                str(user_info.get('digital_id', '')) == target_id or
                str(user_info.get('user_id', '')) == target_id):
                return node
            
            # 递归搜索子节点
            for child in node.get('children', []):
                result = search_in_tree(child, target_id)
                if result:
                    return result
            
            return None
        
        # 在所有BD树中搜索
        for bd_tree in bd_trees:
            result = search_in_tree(bd_tree, user_id)
            if result:
                return result
        
        return None

    def _get_direct_children(self, node: Dict) -> List[str]:
        """获取节点的直接子节点用户ID"""
        children_ids = []
        
        for child in node.get('children', []):
            user_info = child.get('user_info', {})
            # 优先使用member_id，其次digital_id
            user_id = (user_info.get('member_id') or 
                      user_info.get('digital_id') or 
                      user_info.get('user_id'))
            
            if user_id:
                children_ids.append(str(user_id))
        
        return children_ids

    def _get_all_descendants(self, node: Dict) -> List[str]:
        """获取节点的所有后代用户ID"""
        descendants = []
        
        def collect_descendants(current_node: Dict):
            for child in current_node.get('children', []):
                user_info = child.get('user_info', {})
                # 优先使用member_id，其次digital_id
                user_id = (user_info.get('member_id') or 
                          user_info.get('digital_id') or 
                          user_info.get('user_id'))
                
                if user_id:
                    descendants.append(str(user_id))
                
                # 递归收集子节点的后代
                collect_descendants(child)
        
        collect_descendants(node)
        return descendants

    def _convert_to_digital_id(self, user_id: str) -> str:
        """将用户ID转换为digital_id"""
        try:
            # 首先检查是否已经是digital_id格式
            sql = "SELECT digital_id FROM users WHERE digital_id = ? LIMIT 1"
            result = self.db_manager.execute_sql(sql, [user_id])
            if result:
                return user_id

            # 尝试通过member_id查找digital_id
            sql = "SELECT digital_id FROM users WHERE member_id = ? LIMIT 1"
            result = self.db_manager.execute_sql(sql, [user_id])
            if result:
                return result[0]['digital_id']
            
            return ""
            
        except Exception as e:
            self.logger.error(f"转换digital_id失败: {str(e)}")
            return ""

    def _convert_digital_to_member_id(self, digital_id: str) -> str:
        """将digital_id转换为member_id"""
        try:
            sql = "SELECT member_id FROM users WHERE digital_id = ? LIMIT 1"
            result = self.db_manager.execute_sql(sql, [digital_id])
            if result and result[0]['member_id']:
                return str(result[0]['member_id'])
            return ""
            
        except Exception as e:
            self.logger.error(f"转换member_id失败: {str(e)}")
            return ""

    def _convert_ids_to_member_ids(self, user_ids: List[str]) -> Dict[str, str]:
        """转换用户ID到member_id的映射"""
        # 复用批量查询服务的ID转换逻辑
        from .batch_query_service import contract_batch_query_service
        return contract_batch_query_service._convert_ids_to_member_ids(user_ids)

    def _query_abnormal_amounts(self, member_ids: List[str]) -> List[Dict]:
        """查询异常金额数据"""
        # 复用批量查询服务的异常金额查询逻辑
        from .batch_query_service import contract_batch_query_service
        return contract_batch_query_service._query_abnormal_amounts(member_ids)

    def _generate_relation_excel_file(self, target_user: str, all_user_ids: List[str], 
                                    results: List[Dict], converted_ids: Dict[str, str], 
                                    related_users: List[str], include_direct_only: bool) -> str:
        """生成关联查询Excel文件"""
        try:
            import pandas as pd
            from openpyxl import Workbook
            from openpyxl.styles import PatternFill, Font, Alignment
            from openpyxl.utils.dataframe import dataframe_to_rows
            
            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"contract_relation_query_{target_user}_{timestamp}.xlsx"
            file_path = os.path.join(temp_dir, filename)
            
            # 创建工作簿
            wb = Workbook()
            
            # 删除默认工作表
            wb.remove(wb.active)
            
            # 1. 创建关联关系概览表
            self._create_relation_overview_sheet(wb, target_user, related_users, include_direct_only)
            
            # 2. 创建异常金额汇总表
            self._create_abnormal_summary_sheet(wb, all_user_ids, results, converted_ids)
            
            # 3. 创建详细数据表
            self._create_detailed_data_sheet(wb, results)
            
            # 保存文件
            wb.save(file_path)
            
            self.logger.info(f"关联查询Excel文件生成成功: {file_path}")
            return file_path
            
        except Exception as e:
            self.logger.error(f"生成关联查询Excel文件失败: {str(e)}")
            raise

    def _create_relation_overview_sheet(self, wb, target_user: str, related_users: List[str], include_direct_only: bool):
        """创建关联关系概览表"""
        ws = wb.create_sheet("关联关系概览")
        
        # 设置标题
        ws['A1'] = "合约异常金额关联查询报告"
        ws['A1'].font = Font(size=16, bold=True)
        ws.merge_cells('A1:D1')
        
        # 查询信息
        ws['A3'] = "查询信息"
        ws['A3'].font = Font(bold=True)
        
        ws['A4'] = "目标用户ID:"
        ws['B4'] = target_user
        ws['A5'] = "查询模式:"
        ws['B5'] = "仅直接下属" if include_direct_only else "完整下属树"
        ws['A6'] = "关联用户数量:"
        ws['B6'] = len(related_users)
        ws['A7'] = "查询时间:"
        ws['B7'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 关联用户列表
        if related_users:
            ws['A9'] = "关联用户列表"
            ws['A9'].font = Font(bold=True)
            
            for i, user_id in enumerate(related_users, start=10):
                ws[f'A{i}'] = f"关联用户 {i-9}:"
                ws[f'B{i}'] = user_id

    def _create_abnormal_summary_sheet(self, wb, all_user_ids: List[str], results: List[Dict], converted_ids: Dict[str, str]):
        """创建异常金额汇总表"""
        # 复用批量查询服务的Excel生成逻辑，但添加关联关系标识
        from .batch_query_service import contract_batch_query_service
        
        # 这里可以调用批量查询服务的Excel生成方法，然后添加关联关系信息
        # 为了简化，我们创建一个基本的汇总表
        ws = wb.create_sheet("异常金额汇总")
        
        # 设置表头
        headers = ["用户ID", "用户类型", "对敲异常金额", "高频异常金额", "套利异常金额", "刷量异常金额", "总异常金额"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # 填充数据
        target_user = all_user_ids[0] if all_user_ids else ""
        
        for row, user_id in enumerate(all_user_ids, 2):
            member_id = converted_ids.get(user_id, user_id)
            user_results = [r for r in results if str(r.get('member_id', '')) == member_id]
            
            # 计算各类异常金额
            hedge_amount = sum(float(r.get('hedge_amount', 0)) for r in user_results)
            high_freq_amount = sum(float(r.get('high_frequency_amount', 0)) for r in user_results)
            arbitrage_amount = sum(float(r.get('arbitrage_amount', 0)) for r in user_results)
            wash_amount = sum(float(r.get('wash_trading_amount', 0)) for r in user_results)
            total_amount = hedge_amount + high_freq_amount + arbitrage_amount + wash_amount
            
            # 确定用户类型
            user_type = "目标用户" if user_id == target_user else "关联用户"
            
            ws.cell(row=row, column=1, value=user_id)
            ws.cell(row=row, column=2, value=user_type)
            ws.cell(row=row, column=3, value=hedge_amount)
            ws.cell(row=row, column=4, value=high_freq_amount)
            ws.cell(row=row, column=5, value=arbitrage_amount)
            ws.cell(row=row, column=6, value=wash_amount)
            ws.cell(row=row, column=7, value=total_amount)
            
            # 目标用户高亮显示
            if user_id == target_user:
                for col in range(1, 8):
                    ws.cell(row=row, column=col).fill = PatternFill(start_color="FFFF99", end_color="FFFF99", fill_type="solid")

    def _create_detailed_data_sheet(self, wb, results: List[Dict]):
        """创建详细数据表"""
        # 复用批量查询服务的详细数据表创建逻辑
        from .batch_query_service import contract_batch_query_service
        
        ws = wb.create_sheet("详细数据")
        
        if not results:
            ws['A1'] = "无异常数据"
            return
        
        # 设置表头
        headers = ["用户ID", "算法类型", "异常金额", "检测时间", "风险等级"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # 填充数据
        for row, result in enumerate(results, 2):
            ws.cell(row=row, column=1, value=result.get('member_id', ''))
            ws.cell(row=row, column=2, value=result.get('algorithm_type', ''))
            ws.cell(row=row, column=3, value=result.get('total_amount', 0))
            ws.cell(row=row, column=4, value=result.get('created_at', ''))
            ws.cell(row=row, column=5, value=result.get('risk_level', ''))


# 创建全局实例
contract_relation_query_service = ContractRelationQueryService()
