// User Analysis Page Main Entry Point - Refactored Version

// Import Bootstrap CSS and JavaScript
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap-icons/font/bootstrap-icons.css';
import * as bootstrap from 'bootstrap'; // 重要：导入Bootstrap JavaScript以支持下拉菜单等交互功能
// 确保Bootstrap全局可用
window.bootstrap = bootstrap;

// Chart.js is loaded via CDN in HTML
// import Chart from 'chart.js/auto';

// Import unified styles
import '../styles/user-analysis-unified.css';

// Import modular components - using dynamic imports for better compatibility
// Static imports replaced with dynamic loading in init method

/**
 * 用户分析主应用类
 * 协调各个组件的工作
 */
class UserAnalysisApp {
    constructor() {
        this.pageManager = null;
        this.domManager = null;
        this.searchManager = null;
        this.userProfileDisplay = null;
        this.riskAnalysisDisplay = null;
        this.transactionDetailsManager = null;
        this.userBehaviorDisplay = null;
        this.modalManager = null;
        this.coinAnalysis = null;
        this.scoreGauge = null;
        this.directFieldMapper = null;
        
        // 应用状态
        this.state = new AppState();
        
        // 绑定方法上下文
        this.handleSearchSuccess = this.handleSearchSuccess.bind(this);
        this.handleSearchError = this.handleSearchError.bind(this);
    }

    /**
     * 初始化应用
     */
    async init() {
        try {
    
            
            // 动态导入所有组件
            const [
                { default: PageManager },
                { default: DOMManager },
                { default: SearchManager },
                { default: UserProfileDisplay },
                { default: RiskAnalysisDisplay },
                { default: TransactionDetailsManager },
                { default: UserBehaviorDisplay },
                { default: ModalManager },
                { default: CoinWinRateAnalysis },
                { default: ProfessionalScoreGauge },
                { default: DirectFieldMapper },
                utils
            ] = await Promise.all([
                import('../components/PageManager.js'),
                import('../components/DOMManager.js'),
                import('../components/SearchManager.js'),
                import('../components/UserProfileDisplay.js'),
                import('../components/RiskAnalysisDisplay.js'),
                import('../components/TransactionDetailsManager.js'),
                import('../components/UserBehaviorDisplay.js'),
                import('../components/ModalManager.js'),
                import('../components/CoinWinRateAnalysis.js'),
                import('../components/ProfessionalScoreGauge.js'),
                import('../components/DirectFieldMapper.js'),
                import('../components/utils.js')
            ]);
            
            // 将工具函数添加到全局作用域
            window.formatNumber = utils.formatNumber;
            window.formatVolume = utils.formatVolume;
            window.formatTime = utils.formatTime;
            window.formatDirectPercentage = utils.formatDirectPercentage;
            window.getScoreLevel = utils.getScoreLevel;
            window.getScoreColor = utils.getScoreColor;
            window.getScoreLevelClass = utils.getScoreLevelClass;
            window.updateProgressBar = utils.updateProgressBar;
            window.updateScoreBar = utils.updateScoreBar;
            window.showToast = utils.showToast;
            window.copyToClipboard = utils.copyToClipboard;
            window.showLoading = utils.showLoading;
            window.showError = utils.showError;
            window.hideError = utils.hideError;
            window.showResults = utils.showResults;
            window.hideResults = utils.hideResults;
            
            // 1. 初始化页面管理器（包含鉴权检查）
            this.pageManager = PageManager;
            const user = await this.pageManager.initialize();
            if (!user) return; // 鉴权失败，已跳转
            
            // 2. 初始化DOM管理器
            this.domManager = DOMManager;
            this.domManager.initialize();
            
            // 3. 初始化搜索管理器
            this.searchManager = SearchManager;
            this.searchManager.initialize();
            this.searchManager.onSearchSuccess = this.handleSearchSuccess;
            this.searchManager.onSearchError = this.handleSearchError;
            
            // 4. 初始化显示组件
            this.userProfileDisplay = new UserProfileDisplay(this.domManager);
            this.riskAnalysisDisplay = new RiskAnalysisDisplay(this.domManager);
            this.transactionDetailsManager = new TransactionDetailsManager(this.domManager);
            // 🔧 设置全局引用，供showCounterpartyDetails函数使用
            window.transactionDetailsManager = this.transactionDetailsManager;
            this.userBehaviorDisplay = new UserBehaviorDisplay(this.domManager);
            this.modalManager = ModalManager;
            
            // 5. 初始化专业度评分仪表盘
            this.scoreGauge = new ProfessionalScoreGauge('professionalScoreGauge');
            
            // 6. 初始化币种分析组件
            this.coinAnalysis = new CoinWinRateAnalysis('coinAnalysisContainer');
            
            // 🚀 7. 初始化直接字段映射器
            this.directFieldMapper = new DirectFieldMapper();
            
            // 8. 初始化事件监听器
            this.initializeEventListeners();
            
            // 9. 加载初始数据
            await this.loadInitialData();
            

            
        } catch (error) {
            console.error('❌ 应用初始化失败:', error);
            if (window.showError) {
                window.showError('应用初始化失败: ' + error.message);
            } else {
                alert('应用初始化失败: ' + error.message);
            }
        }
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        const elements = this.domManager.elements;
        
        // 关联分析标签切换
        if (elements.associationTabs) {
            elements.associationTabs.forEach(tab => {
                tab.addEventListener('click', (e) => {
                    const tabName = e.target.dataset.tab;
                    this.switchAssociationTab(tabName);
                });
            });
        }
        
        // 用户行为分析标签切换
        if (elements.behaviorAnalysisTabs) {
            elements.behaviorAnalysisTabs.forEach(tab => {
                tab.addEventListener('click', (e) => {
                    const tabName = e.target.dataset.tab;
                    this.switchBehaviorTab(tabName);
                });
            });
        }
        
        // 指标标签切换
        if (elements.metricTabs) {
            elements.metricTabs.forEach(tab => {
                tab.addEventListener('click', (e) => {
                    const tabName = e.target.dataset.tab;
                    this.switchMetricTab(tabName);
                });
            });
        }
        
        // 交易详情相关事件
        this.initializeTransactionEvents();
        
        // 模态框事件
        this.initializeModalEvents();
    }

    /**
     * 初始化交易详情相关事件
     */
    initializeTransactionEvents() {
        const elements = this.domManager.elements;

        // 分页
        if (elements.prevPage) {
            elements.prevPage.addEventListener('click', () => {
                if (this.state.currentPage > 1) {
                    this.state.currentPage--;
                    this.displayTransactions();
                }
            });
        }
        
        if (elements.nextPage) {
            elements.nextPage.addEventListener('click', () => {
                const totalPages = Math.ceil(this.state.filteredTransactions.length / this.state.pageSize);
                if (this.state.currentPage < totalPages) {
                    this.state.currentPage++;
                    this.displayTransactions();
                }
            });
        }
    }

    /**
     * 初始化模态框事件
     */
    initializeModalEvents() {
        // 关闭模态框事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal') || e.target.classList.contains('close')) {
                this.closeAllModals();
            }
        });
        
        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });
    }

    /**
     * 加载初始数据
     */
    async loadInitialData() {
        try {
            // 加载可用任务
            await this.searchManager.loadAvailableTasks();
            
        } catch (error) {
            console.error('加载初始数据失败:', error);
        }
    }

    /**
     * 处理搜索成功 - 重构后的统一处理逻辑
     */
    async handleSearchSuccess(userData) {
        try {

            
            // 检查是否为完整分析数据
            if (userData.complete_data) {
                // 新的统一数据处理逻辑
                this.displayCompleteUserAnalysis(userData, '统一搜索');
            } else {
                // 兼容旧的分步处理逻辑
                await this.handleLegacySearchSuccess(userData);
            }
            
        } catch (error) {
            console.error('❌ 处理用户数据时出错:', error);
            window.showError('数据处理失败: ' + error.message);
        }
    }

    /**
     * 显示完整用户分析数据
     */
    displayCompleteUserAnalysis(completeData, searchMethod) {
        if (!completeData || !completeData.complete_data) {
            window.showError('未获取到完整的分析数据');
            return;
        }
        
        const data = completeData.complete_data;
        
        try {
            // 更新应用状态
            this.state.currentUserData = completeData;
            
            // 1. 显示搜索区域数据
            this.displaySearchAreaData(data.search_area);
            
            // 2. 显示用户档案 - 🚀 修复：使用UserProfileDisplay组件
            if (this.userProfileDisplay) {
                const profileData = {
                    ...data.user_profile,
                    search_method: searchMethod
                };

                this.userProfileDisplay.display(profileData);
            } else {
                // 备用：直接DOM操作（保持兼容性）
                this.displayUserProfileComplete(data.user_profile, searchMethod);
            }
            
            // 3. 显示专业度评分仪表盘
            this.displayProfessionalDashboardComplete(data.professional_dashboard);
            
            // 显示分析时间和数据质量
            this.updateElementText('analysisDate', '分析时间: ' + window.formatTime(completeData.analysis_timestamp));
            // 注释：dataQualityScore和analysisConfidence元素在当前HTML中不存在，跳过更新
            // this.updateElementText('dataQualityScore', completeData.data_quality_score_display || completeData.data_quality_score || '0');
            // this.updateElementText('analysisConfidence', completeData.analysis_confidence || '中等');
            
            // 🔧 修改：移除重复的基础指标显示调用，数据已统一传递给UserBehaviorDisplay


            // 10. 显示衍生分析指标（4个维度）
            this.displayDerivedAnalysisComplete(data.derived_analysis);
            
            // 🚀 协同工作：DirectFieldMapper处理基础字段，UserBehaviorDisplay处理复杂逻辑
            if (this.directFieldMapper) {

                const mappingResult = this.directFieldMapper.mapAndDisplay(completeData);

            }

            // 🚀 UserBehaviorDisplay处理专业度评分、进度条等复杂逻辑
            // 🔧 注释：废弃复杂的数据结构构建，改为直接传递原始数据
            // if (this.userBehaviorDisplay) {
            //     console.log('🚀 使用UserBehaviorDisplay处理复杂逻辑');
            //     const behaviorData = this.buildBehaviorDataStructure(data, completeData);
            //     this.userBehaviorDisplay.display(behaviorData);
            // }

            // 🚀 修复：使用UserBehaviorDisplay的独立方法处理各类数据
            if (this.userBehaviorDisplay) {
    
                
                try {
                    // 1. 显示专业度评分（最重要的）
                    if (data.professional_scores || data.professional_dashboard) {
            
                        this.userBehaviorDisplay.displayProfessionalScores(data.professional_scores || data.professional_dashboard);
                    }
                    
                    // 2. 显示衍生分析指标
                    if (data.derived_analysis) {
            
                        this.userBehaviorDisplay.displayDerivedMetrics(data.derived_analysis);
                    }
                    
                    // 3. 显示交易偏好
                    if (data.trading_preferences) {
            
                        this.userBehaviorDisplay.displayTradingPreferences(data.trading_preferences);
                    }
                    
                    // 4. 显示币种分析摘要
                    if (data.coin_analysis) {
            
                        this.userBehaviorDisplay.displayCoinAnalysisSummary(data.coin_analysis);
                    }
                    
                    // 5. 显示异常交易分析
                    if (data.abnormal_analysis) {
            
                        this.userBehaviorDisplay.displayAbnormalAnalysis(data.abnormal_analysis);
                    }
                    
                    // 6. 显示数据质量信息
        
                    this.userBehaviorDisplay.displayDataQualityInfo({
                        analysis_timestamp: completeData.analysis_timestamp,
                        data_quality_score: completeData.data_quality_score,
                        analysis_confidence: completeData.analysis_confidence
                    });
                    
        
                    
                } catch (error) {
                    console.error('UserBehaviorDisplay显示过程中出错:', error);
                }
                
                // 构建行为数据用于用户档案更新
                const behaviorData = {
                    basic_metrics: {
                        ...data.trading_scale_metrics,
                        ...data.pnl_metrics,
                        ...data.holding_time_metrics,
                        ...data.order_type_metrics,
                        ...data.risk_control_metrics,
                        ...data.fund_scale_metrics
                    },
                    professional_scores: data.professional_scores || data.professional_dashboard
                };
                
                // 更新用户档案中的专业度评分预览
                if (this.userProfileDisplay) {
                    this.userProfileDisplay.updateProfessionalScorePreview(behaviorData);
                }
            }
            
            // 11-17. 显示其他分析模块
            this.displayTradingPreferencesComplete(data.trading_preferences);
            this.displayCoinAnalysisComplete(data.coin_analysis);
            // this.displayHedgeStatisticsComplete(data.hedge_statistics);  // 对冲统计显示已移除
            this.displayAbnormalAnalysisComplete(data.abnormal_analysis);
            this.displayRiskSummaryComplete(data.risk_summary);

            // 🔧 添加关联数据调试
            console.log('🔍 准备显示关联分析，data.associations:', data.associations);
            console.log('🔍 associations对象的所有键:', Object.keys(data.associations || {}));
            console.log('🔍 associations对象的完整内容:', JSON.stringify(data.associations, null, 2));
            this.displayAssociationsComplete(data.associations);
            // 🔧 修复：正确传递风险交易数据
            if (data.transaction_details && data.transaction_details.risk_transactions) {
                this.displayTransactionDetailsComplete(data.transaction_details.risk_transactions);
            } else {
                this.displayTransactionDetailsComplete(data.transaction_details);
            }

            // 🔧 额外检查：查看是否有其他包含风险交易的字段
            if (data.transaction_details.risk_by_type) {
                if (data.transaction_details.risk_by_type.wash_trading) {
                    console.log('🔍 wash_trading数据:', data.transaction_details.risk_by_type.wash_trading);
                    if (data.transaction_details.risk_by_type.wash_trading.transactions) {
                        console.log('🔍 找到wash_trading.transactions，长度:', data.transaction_details.risk_by_type.wash_trading.transactions.length);
                        // 如果risk_transactions为空，但wash_trading.transactions有数据，使用它
                        if (data.transaction_details.risk_transactions.length === 0 &&
                            data.transaction_details.risk_by_type.wash_trading.transactions.length > 0) {
                            console.log('✅ 使用wash_trading.transactions作为备用数据源');
                            this.displayRiskTransactionsList(data.transaction_details.risk_by_type.wash_trading.transactions);
                        }
                    }
                }
            }
            
            // 显示结果区域
            window.showResults();
            
    
            
        } catch (error) {
            console.error('显示完整分析数据时出错:', error);
            window.showError('数据显示失败: ' + error.message);
        }
    }

    /**
     * 兼容旧的分步处理逻辑
     */
    async handleLegacySearchSuccess(userData) {
        // 更新应用状态
        this.state.currentUserData = userData;
        
        // 构建用户档案数据 - 根据实际返回的数据结构
        const profileData = {
            member_id: userData.member_id,
            digital_id: userData.digital_id || '-',
            user_type: userData.user_type || '普通用户',
            bd_name: userData.bd_relationship?.bd_name || '-',
            total_risks: userData.risk_summary?.total_risks || 0,
            last_activity: userData.last_activity || '-',
            search_method: userData.search_method || '直接搜索'
        };
        
        // 显示用户档案
        this.userProfileDisplay.display(profileData);
        
        // 显示风险摘要
        if (userData.risk_summary) {
            this.riskAnalysisDisplay.displayRiskSummary(userData.risk_summary);
        }
        
        // 显示关联分析
        if (userData.association_analysis) {
            this.riskAnalysisDisplay.displayAssociationAnalysis(userData.association_analysis);
        }
        
        // 🔧 强制显示交易详情 - 临时解决方案
        console.log('🔍 开始处理交易详情...');
        console.log('🔍 userData.transaction_details存在:', !!userData.transaction_details);

        if (userData.transaction_details) {
            console.log('🔍 显示交易详情数据:', userData.transaction_details);
            console.log('🔍 transaction_details结构:', Object.keys(userData.transaction_details));

            const riskTransactions = userData.transaction_details.risk_transactions || [];
            console.log('🔍 风险交易列表长度:', riskTransactions.length);

            // 🔧 强制显示：如果有风险交易数据，直接调用显示方法
            if (riskTransactions.length > 0) {
                console.log('✅ 强制调用displayRiskTransactionsList');
                // 延迟执行确保DOM已准备好
                setTimeout(() => {
                    this.displayRiskTransactionsList(riskTransactions);
                }, 100);
            }
        }
        
        // 加载用户行为分析数据
        const memberId = userData.member_id || (userData.profile && userData.profile.member_id);
        if (memberId) {
            await this.loadUserBehaviorData(memberId);
        } else {
            console.warn('无法获取用户ID，跳过用户行为分析');
            // 显示用户行为分析不可用状态
            if (this.userBehaviorDisplay) {
                this.userBehaviorDisplay.showDataNotAvailable();
            }
        }
        
        // 显示结果区域
        window.showResults();
        
        console.log('✅ 用户数据处理完成（兼容模式）');
    }

    /**
     * 处理搜索错误
     */
    handleSearchError(error) {
        console.error('搜索失败:', error);
        showError(error.message || '搜索用户数据失败');
        hideResults();
    }

    /**
     * 加载用户行为分析数据
     */
    async loadUserBehaviorData(memberId) {
        try {
            const behaviorService = this.pageManager.getUserBehaviorService();
            if (!behaviorService) {
                console.warn('用户行为分析服务不可用');
                return;
            }
            
            showLoading(true);
            const behaviorData = await behaviorService.getUserBehaviorAnalysis(memberId);
            
            if (behaviorData) {
                this.state.currentBehaviorData = behaviorData;
                this.userBehaviorDisplay.display(behaviorData);
                // 同时更新用户档案中的专业度评分预览
                this.userProfileDisplay.updateProfessionalScorePreview(behaviorData);
            } else {
                this.userBehaviorDisplay.showDataNotAvailable();
            }
            
        } catch (error) {
            console.error('加载用户行为数据失败:', error);
            showError('用户行为分析数据加载失败: ' + error.message);
        } finally {
            showLoading(false);
        }
    }

    // ================================
    // 完整分析数据显示函数
    // ================================

    /**
     * 显示搜索区域数据
     */
    displaySearchAreaData(searchArea) {
        // 更新任务下拉列表
        this.updateTaskDropdowns(searchArea.contract_tasks, searchArea.agent_tasks);
    }

    /**
     * 更新任务下拉列表
     */
    updateTaskDropdowns(contractTasks, agentTasks) {
        // 更新合约任务下拉列表
        const contractSelect = document.getElementById('contractTaskSelect');
        if (contractSelect && contractTasks) {
            contractSelect.innerHTML = '<option value="">选择合约任务</option>';
            contractTasks.forEach(task => {
                const option = document.createElement('option');
                // 修复：统一使用task_id作为value，确保与后端数据结构一致
                option.value = task.task_id || task.id;
                // 修复：优先使用name，回退到其他字段
                const taskName = task.name || task.task_name || task.file_path || `任务_${(task.task_id || task.id)?.substring(0, 8)}`;
                option.textContent = `${taskName} (${task.status})`;
                contractSelect.appendChild(option);
            });
        }

        // 更新代理任务下拉列表
        const agentSelect = document.getElementById('agentTaskSelect');
        if (agentSelect && agentTasks) {
            agentSelect.innerHTML = '<option value="">选择代理任务</option>';
            agentTasks.forEach(task => {
                const option = document.createElement('option');
                // 修复：统一使用task_id作为value，确保与后端数据结构一致
                option.value = task.task_id || task.id;
                // 修复：优先使用name，回退到其他字段
                const taskName = task.name || task.task_name || task.file_path || `任务_${(task.task_id || task.id)?.substring(0, 8)}`;
                option.textContent = `${taskName} (${task.status})`;
                agentSelect.appendChild(option);
            });
        }
    }

    /**
     * 显示用户档案完整数据
     */
    displayUserProfileComplete(profile, searchMethod) {
        this.updateElementText('profileMemberId', profile.member_id || '-');
        this.updateElementText('profileDigitalId', profile.digital_id || '-');
        this.updateElementText('profileUserType', profile.user_type || '普通用户');
        this.updateElementText('profileBdName', profile.bd_name || '-');
        this.updateElementText('profileFundScale', profile.fund_scale_range || '未知规模');
        this.updateElementText('profileTotalRisks', profile.total_risks || '0');
        this.updateElementText('profileLastActivity', profile.last_activity || '-');
        this.updateElementText('profileSearchMethod', searchMethod || '自动识别');
        // 注释：profileConfidenceLevel元素在当前HTML中不存在，跳过更新
        // this.updateElementText('profileConfidenceLevel', profile.confidence_level || '中等');
    }

    /**
     * 显示专业度评分仪表盘完整数据 - 修改：只处理仪表盘绘制，避免重复显示
     */
    displayProfessionalDashboardComplete(dashboard) {
        // 🔧 修改：只处理仪表盘绘制，其他显示由UserBehaviorDisplay组件统一处理
        if (this.scoreGauge) {
            this.scoreGauge.draw(dashboard.total_score || 0);
        }
        
        // 🔧 移除重复的评分条和文本更新，由UserBehaviorDisplay处理
        // 注释：confidenceLevel元素在当前HTML中不存在，跳过更新
        // this.updateElementText('confidenceLevel', dashboard.confidence_level || '中等');
        

    }

    /**
     * 🔧 废弃：构建用户行为分析数据结构 - 统一数据传递格式
     * 注释原因：改为使用DirectFieldMapper直接映射，避免复杂的数据结构转换
     */
    // buildBehaviorDataStructure(data, completeData) {
    //     // 数据结构已经修复，合并所有基础指标到统一对象中

    //     return {
    //         // 🚀 修复：合并所有基础指标到统一的basic_metrics对象中
    //         basic_metrics: {
    //             // 交易规模数据
    //             ...data.trading_scale_metrics,
    //             // 盈亏统计数据
    //             ...data.pnl_metrics,
    //             // 持仓时间数据
    //             ...data.holding_time_metrics,
    //             // 订单类型数据
    //             ...data.order_type_metrics,
    //             // 风险控制数据
    //             ...data.risk_control_metrics,
    //             // 资金规模数据
    //             ...data.fund_scale_metrics,
    //             // 🔧 修复：position_consistency是基础字段，直接从基础数据中提取
    //             position_consistency: data.basic_data?.position_consistency ||
    //                                 data.trading_scale_metrics?.position_consistency ||
    //                                 data.risk_control_metrics?.position_consistency
    //         },
    //         basic_metrics_extended: {
    //             max_trade_amount: data.trading_scale_metrics?.max_single_trade,
    //             min_trade_amount: data.trading_scale_metrics?.min_single_trade,
    //             max_trade_contract: data.trading_scale_metrics?.max_trade_contract,
    //             min_trade_contract: data.trading_scale_metrics?.min_trade_contract,
    //             ...data.pnl_metrics
    //         },

    //         // 保留专门的指标对象（向后兼容）
    //         holding_time_metrics: data.holding_time_metrics,
    //         order_type_metrics: data.order_type_metrics,
    //         risk_control_metrics: data.risk_control_metrics,
    //         fund_scale_metrics: data.fund_scale_metrics,

    //         // 分析结果 - 确保结构正确
    //         derived_analysis: data.derived_analysis,
    //         professional_scores: data.professional_scores || data.professional_dashboard,  // 🚀 修复：优先使用professional_scores

    //         // 其他分析数据
    //         trading_preferences: data.trading_preferences,
    //         coin_analysis: data.coin_analysis,
    //         // hedge_statistics: data.hedge_statistics,  // 对冲统计已移除
    //         abnormal_analysis: data.abnormal_analysis,

    //         // 元数据
    //         analysis_timestamp: completeData.analysis_timestamp,
    //         data_quality_score: completeData.data_quality_score,
    //         analysis_confidence: completeData.analysis_confidence
    //     };
    // }

    /**
     * 显示衍生分析指标 - 注释掉前端计算，保留字段映射
     */
    displayDerivedAnalysisComplete(analysis) {
        // 盈利能力 - 直接使用后端数据
        const profitability = analysis.profitability || {};
        this.updateElementText('derivedWinRate', profitability.win_rate_display || profitability.win_rate || '--');
        this.updateElementText('derivedProfitLossRatio', profitability.profit_loss_ratio_display || profitability.profit_loss_ratio || '--');
        this.updateElementText('derivedProfitFactor', profitability.profit_factor_display || profitability.profit_factor || '--');
        this.updateElementText('derivedProfitConsistency', profitability.profit_consistency_display || profitability.consistency || '--');
        this.updateElementText('profitabilityDimensionTotal', profitability.total_score_display || `总分: ${profitability.total_score || 0}/100`);

        // 风险控制 - 直接使用后端数据
        const riskControl = analysis.risk_control || {};
        this.updateElementText('derivedAvgLeverage', riskControl.avg_leverage_display || riskControl.avg_leverage || '--');
        this.updateElementText('derivedMaxLeverage', riskControl.max_leverage_display || riskControl.max_leverage || '--');
        this.updateElementText('derivedLeverageStability', riskControl.leverage_stability_display || riskControl.leverage_stability || '--');
        this.updateElementText('derivedMaxSingleLoss', riskControl.max_single_loss_ratio_display || window.formatDirectPercentage(riskControl.max_single_loss_ratio || 0));
        this.updateElementText('riskControlDimensionTotal', riskControl.total_score_display || `总分: ${riskControl.total_score || 0}/100`);

        // 交易行为 - 直接使用后端数据
        const tradingBehavior = analysis.trading_behavior || {};
        this.updateElementText('derivedTradingFrequency', tradingBehavior.trading_frequency_display || tradingBehavior.trading_frequency || '--');
        this.updateElementText('derivedMarketOrderRatio', tradingBehavior.market_order_ratio_display || tradingBehavior.market_order_ratio || '--');
        this.updateElementText('derivedDurationRatio', tradingBehavior.duration_ratio_display || tradingBehavior.duration_ratio || '--');
        this.updateElementText('derivedPositionConsistency', tradingBehavior.position_consistency_display || tradingBehavior.position_consistency || '--');
        this.updateElementText('tradingBehaviorDimensionTotal', tradingBehavior.total_score_display || `总分: ${tradingBehavior.total_score || 0}/100`);

        // 市场理解 - 直接使用后端数据
        const marketUnderstanding = analysis.market_understanding || {};
        this.updateElementText('derivedTimingAbility', marketUnderstanding.timing_ability_display || marketUnderstanding.timing_ability || '--');
        this.updateElementText('derivedRiskDiscipline', marketUnderstanding.risk_discipline_display || marketUnderstanding.risk_discipline || '--');
        this.updateElementText('derivedExecutionEfficiency', marketUnderstanding.execution_efficiency_display || marketUnderstanding.execution_efficiency || '--');
        this.updateElementText('marketUnderstandingDimensionTotal', marketUnderstanding.total_score_display || `总分: ${marketUnderstanding.total_score || 0}/100`);
        
        // 使用后端提供的评分数据
        this.updateElementText('derivedWinRateScore', analysis.profitability.win_rate_score_display || `+${analysis.profitability.win_rate_score || 0}分`);
        this.updateElementText('derivedProfitLossRatioScore', analysis.profitability.profit_loss_ratio_score_display || `+${analysis.profitability.profit_loss_ratio_score || 0}分`);
        this.updateElementText('derivedProfitFactorScore', analysis.profitability.profit_factor_score_display || `+${analysis.profitability.profit_factor_score || 0}分`);
        this.updateElementText('derivedProfitConsistencyScore', analysis.profitability.consistency_score_display || `+${analysis.profitability.consistency_score || 0}分`);
        
        this.updateElementText('derivedAvgLeverageScore', analysis.risk_control.avg_leverage_score_display || `+${analysis.risk_control.avg_leverage_score || 0}分`);
        this.updateElementText('derivedMaxLeverageScore', analysis.risk_control.max_leverage_score_display || `+${analysis.risk_control.max_leverage_score || 0}分`);
        this.updateElementText('derivedLeverageStabilityScore', analysis.risk_control.leverage_stability_score_display || `+${analysis.risk_control.leverage_stability_score || 0}分`);
        this.updateElementText('derivedMaxSingleLossScore', analysis.risk_control.max_single_loss_score_display || `+${analysis.risk_control.max_single_loss_score || 0}分`);
        
        this.updateElementText('derivedTradingFrequencyScore', analysis.trading_behavior.trading_frequency_score_display || `+${analysis.trading_behavior.trading_frequency_score || 0}分`);
        this.updateElementText('derivedMarketOrderRatioScore', analysis.trading_behavior.market_order_ratio_score_display || `+${analysis.trading_behavior.market_order_ratio_score || 0}分`);
        this.updateElementText('derivedDurationRatioScore', analysis.trading_behavior.duration_ratio_score_display || `+${analysis.trading_behavior.duration_ratio_score || 0}分`);
        this.updateElementText('derivedPositionConsistencyScore', analysis.trading_behavior.position_consistency_score_display || `+${analysis.trading_behavior.position_consistency_score || 0}分`);
        
        this.updateElementText('derivedTimingAbilityScore', analysis.market_understanding.timing_ability_score_display || `+${analysis.market_understanding.timing_ability_score || 0}分`);
        this.updateElementText('derivedRiskDisciplineScore', analysis.market_understanding.risk_discipline_score_display || `+${analysis.market_understanding.risk_discipline_score || 0}分`);
        this.updateElementText('derivedExecutionEfficiencyScore', analysis.market_understanding.execution_efficiency_score_display || `+${analysis.market_understanding.execution_efficiency_score || 0}分`);
    }

    /**
     * 显示交易偏好分析
     */
    displayTradingPreferencesComplete(preferences) {
        // 币种偏好分布 - 修复字段名匹配和百分比格式化
        const coinPref = preferences.coin_preference;
        if (coinPref) {
            // 使用正确的字段名和格式化方法
            this.updateElementText('mainstreamPercentage', window.formatDirectPercentage(coinPref.mainstream_percentage || 0));
            this.updateElementText('altcoinPercentage', window.formatDirectPercentage(coinPref.altcoin_percentage || 0));
            this.updateElementText('defiPercentage', window.formatDirectPercentage(coinPref.defi_percentage || 0));
            this.updateElementText('othersPercentage', window.formatDirectPercentage(coinPref.others_percentage || 0));

            // 更新进度条 - 直接使用百分比数值（updateProgressBar已优化处理）
            window.updateProgressBar('mainstreamFill', coinPref.mainstream_percentage || 0);
            window.updateProgressBar('altcoinFill', coinPref.altcoin_percentage || 0);
            window.updateProgressBar('defiFill', coinPref.defi_percentage || 0);
            window.updateProgressBar('othersFill', coinPref.others_percentage || 0);
        }
        
        // 偏好合约 - 🔧 修复：确保 favoriteContracts 是数组类型
        const favoriteContractsRaw = preferences.coin_preference?.favorite_contracts || [];
        let favoriteContracts = [];
        if (Array.isArray(favoriteContractsRaw)) {
            favoriteContracts = favoriteContractsRaw;
        } else if (favoriteContractsRaw && typeof favoriteContractsRaw === 'object') {
            // 如果是对象，尝试转换为数组
            favoriteContracts = Object.values(favoriteContractsRaw);
        }
        this.updateElementText('favoriteContracts', favoriteContracts.slice(0, 5).join(', ') || '无');
        
        // 🚀 修复：活跃时段 - 处理新的数据格式，支持多种字段名
        const peakHoursData = preferences.time_preference?.peak_trading_hours || preferences.time_preference?.peak_hours;
        const peakHoursText = this.formatPeakTradingHours(peakHoursData);
        this.updateElementText('peakHours', peakHoursText || '无明显偏好');

        // 🚀 新增：只调用热力图生成，不影响币种偏好显示
        if (this.userBehaviorDisplay && peakHoursData) {

            // 🔧 修复：直接调用热力图生成方法，避免影响币种偏好
            this.userBehaviorDisplay.generateTimeHeatmapFromPeakHours(peakHoursData);
            this.userBehaviorDisplay.updatePeakHoursText(peakHoursData);
        }

        // 风险偏好
        this.updateElementText('riskAppetiteLevel', preferences.risk_preference?.risk_appetite_level || '--');
        this.updateElementText('volatilityPreference', preferences.risk_preference?.volatility_preference || '中等');
        this.updateElementText('diversificationScore', preferences.risk_preference?.diversification_score_display || preferences.risk_preference?.diversification_score || '--');
    }

    /**
     * 显示币种分析
     */
    displayCoinAnalysisComplete(coinAnalysis) {


        // 🚀 更新币种胜率分析组件
        if (this.coinAnalysis) {

            this.coinAnalysis.updateData(coinAnalysis);
        }

        // 优势币种
        const advantageCoins = coinAnalysis.advantage_coins || [];
        this.updateElementText('advantageCoins', advantageCoins.slice(0, 5).join(', ') || '无');

        // 币种专长总结
        this.updateElementText('coinExpertiseSummary', coinAnalysis.coin_expertise_summary || '暂无分析');

        // 平均胜率 - 直接使用后端格式化数据
        this.updateElementText('avgCoinWinRate', coinAnalysis.avg_coin_win_rate_display || coinAnalysis.avg_coin_win_rate || '--');

        // 币种排行（如果有相应的显示区域）
        this.displayCoinRanking(coinAnalysis.coin_performance_ranking || []);
    }

    /**
     * 显示币种排行
     */
    displayCoinRanking(ranking) {
        const rankingContainer = document.getElementById('coinRankingContainer');
        if (!rankingContainer || !ranking.length) return;

        const html = ranking.slice(0, 10).map((coin, index) => `
            <div class="coin-rank-item">
                <span class="rank">${index + 1}</span>
                <span class="coin-name">${coin.contract_name}</span>
                <span class="win-rate">${coin.win_rate_display || coin.win_rate || '--'}</span>
                <span class="trades">${coin.total_trades}笔</span>
            </div>
        `).join('');
        
        rankingContainer.innerHTML = html;
    }

    /**
     * 显示对冲统计 - 已移除
     * 对冲功能已被完全移除，如需恢复请参考版本控制历史
     */
    // displayHedgeStatisticsComplete(hedgeStats) {
    //     // 对冲统计显示功能已移除
    // }

    /**
     * 显示异常分析
     */
    displayAbnormalAnalysisComplete(abnormalAnalysis) {
        // 对敲交易
        this.updateElementText('washTradingVolume', window.formatNumber(abnormalAnalysis.wash_trading_volume || 0) + ' USDT');
        this.updateElementText('washTradingCount', (abnormalAnalysis.wash_trading_count || 0) + ' 笔');
        // 🔧 修复：格式化对敲交易比例为百分比（保留2位小数）
        const washRatio = abnormalAnalysis.wash_trading_ratio;
        const formattedWashRatio = washRatio ? (washRatio * 100).toFixed(2) + '%' : '--';
        this.updateElementText('washTradingRatio', abnormalAnalysis.wash_trading_ratio_display || formattedWashRatio);
        
        // 高频交易 - 直接使用后端格式化数据
        this.updateElementText('highFreqVolume', abnormalAnalysis.high_freq_volume_display || abnormalAnalysis.high_freq_volume || '--');
        this.updateElementText('highFreqCount', abnormalAnalysis.high_frequency_count_display || abnormalAnalysis.high_frequency_count || '--');
        // 🔧 修复：格式化高频交易比例为百分比（保留2位小数）
        const highFreqRatio = abnormalAnalysis.high_freq_ratio || abnormalAnalysis.high_frequency_ratio;
        const formattedHighFreqRatio = highFreqRatio ? (highFreqRatio * 100).toFixed(2) + '%' : '--';
        this.updateElementText('highFreqRatio', abnormalAnalysis.high_freq_ratio_display || formattedHighFreqRatio);
        
        // 资金费率套利 - 直接使用后端格式化数据
        this.updateElementText('fundingArbitrageVolume', abnormalAnalysis.funding_arbitrage_volume_display || abnormalAnalysis.funding_arbitrage_volume || '--');
        this.updateElementText('fundingArbitrageCount', abnormalAnalysis.arbitrage_count_display || abnormalAnalysis.arbitrage_count || '--');
        // 🔧 修复：格式化资金费率套利比例为百分比（保留2位小数）
        const arbitrageRatio = abnormalAnalysis.funding_arbitrage_ratio;
        const formattedArbitrageRatio = arbitrageRatio ? (arbitrageRatio * 100).toFixed(2) + '%' : '--';
        this.updateElementText('fundingArbitrageRatio', abnormalAnalysis.funding_arbitrage_ratio_display || formattedArbitrageRatio);
        
        // 异常交易汇总 - 直接使用后端格式化数据
        this.updateElementText('totalAbnormalVolume', abnormalAnalysis.total_abnormal_volume_display || abnormalAnalysis.total_abnormal_volume || '--');
        this.updateElementText('totalAbnormalRatio', abnormalAnalysis.total_abnormal_ratio_display || abnormalAnalysis.total_abnormal_ratio || '--');
        this.updateElementText('riskEventsCount', (abnormalAnalysis.risk_events_count || 0) + ' 个');
        this.updateElementText('realTradingScale', window.formatNumber(abnormalAnalysis.real_trading_scale || 0) + ' USDT');
    }

    /**
     * 显示风险摘要
     */
    displayRiskSummaryComplete(riskSummary) {
        this.updateElementText('summaryTotalRisks', riskSummary.total_risks || '0');
        this.updateElementText('summaryMaxScore', riskSummary.max_score_display || riskSummary.max_score || '--');
        this.updateElementText('summaryTotalVolume', window.formatNumber(riskSummary.total_volume || 0));
        this.updateElementText('summaryRiskTypes', riskSummary.risk_types || '0');
        
        // 风险分类
        const riskCategories = riskSummary.risk_categories || [];
        this.updateElementText('riskCategories', riskCategories.join(', ') || '无');
    }

    /**
     * 显示关联分析
     */
    displayAssociationsComplete(associations) {
        // 🔧 添加调试信息
        console.log('🔍 displayAssociationsComplete 被调用，associations:', associations);

        if (!associations) {
            console.warn('⚠️ associations 数据为空');
            return;
        }

        console.log('📊 关联数据统计:');
        console.log('  - same_ip_count:', associations.same_ip_count);
        console.log('  - same_device_count:', associations.same_device_count);
        console.log('  - both_shared_count:', associations.both_shared_count);
        console.log('  - same_ip_users 长度:', (associations.same_ip_users || []).length);
        console.log('  - same_device_users 长度:', (associations.same_device_users || []).length);
        console.log('  - both_shared_users 长度:', (associations.both_shared_users || []).length);

        this.updateElementText('sameIpCount', associations.same_ip_count || '0');
        this.updateElementText('sameDeviceCount', associations.same_device_count || '0');
        this.updateElementText('bothSharedCount', associations.both_shared_count || '0');

        // 显示关联用户列表
        this.displayAssociatedUsers('sameIpUsers', associations.same_ip_users || []);
        this.displayAssociatedUsers('sameDeviceUsers', associations.same_device_users || []);
        this.displayAssociatedUsers('bothSharedUsers', associations.both_shared_users || []);
    }

    /**
     * 显示关联用户列表
     */
    displayAssociatedUsers(containerId, users) {
        // 🔧 添加调试信息
        console.log(`🔍 displayAssociatedUsers 被调用: containerId=${containerId}, users数量=${users ? users.length : 0}`);

        const container = document.getElementById(containerId);
        if (!container) {
            console.warn(`⚠️ 找不到容器元素: ${containerId}`);
            return;
        }

        if (!users || !users.length) {
            console.log(`📝 ${containerId}: 显示"暂无关联用户"`);
            container.innerHTML = '<div class="no-data">暂无关联用户</div>';
            return;
        }

        console.log(`✅ ${containerId}: 准备显示 ${users.length} 个用户`);
        if (users.length > 0) {
            console.log(`📋 第一个用户数据:`, users[0]);
        }

        const html = users.slice(0, 10).map(user => `
            <div class="associated-user-item">
                <div class="user-info">
                    <div class="user-header">
                        <span class="user-id">${user.member_id}</span>
                        ${user.bd_name ? `<span class="bd-name">[${user.bd_name}]</span>` : ''}
                    </div>
                    <div class="shared-details">
                        ${user.shared_ip ? `<div class="shared-info ip-info">
                            <i class="bi bi-globe"></i> IP: <code>${user.shared_ip}</code>
                        </div>` : ''}
                        ${user.shared_device ? `<div class="shared-info device-info">
                            <i class="bi bi-phone"></i> 设备: <code>${user.shared_device.substring(0, 16)}...</code>
                        </div>` : ''}
                        ${user.last_activity ? `<div class="activity-info">
                            <i class="bi bi-clock"></i> 最近活动: ${user.last_activity}
                        </div>` : ''}
                    </div>
                </div>

            </div>
        `).join('');

        container.innerHTML = html;
    }

    /**
     * 显示交易详情
     */
    displayTransactionDetailsComplete(transactionDetails) {
        if (Array.isArray(transactionDetails)) {
            // 🔧 修复：如果是数组，直接使用数组作为风险交易列表
            const riskTransactions = transactionDetails;

            this.updateElementText('totalRiskTransactions', riskTransactions.length.toString());

            // 显示风险交易列表

            this.displayRiskTransactionsList(riskTransactions);

            // 按类型显示风险交易统计 - 跳过，因为没有risk_by_type数据
            // 跳过displayRiskTransactionsByType，因为数据是数组格式
        } else {


            const riskTransactions = transactionDetails.risk_transactions || [];
            console.log('🔍 提取的riskTransactions:', riskTransactions);
            console.log('🔍 riskTransactions长度:', riskTransactions.length);

            this.updateElementText('totalRiskTransactions', transactionDetails.total_risk_transactions || '0');

            // 显示风险交易列表

            this.displayRiskTransactionsList(riskTransactions);

            // 按类型显示风险交易统计
            this.displayRiskTransactionsByType(transactionDetails.risk_by_type || {});
        }
    }

    /**
     * 显示风险交易列表
     */
    displayRiskTransactionsList(transactions) {
        const container = document.getElementById('transactionsList');

        if (!container) {
            console.error('❌ 未找到transactionsList容器');
            return;
        }

        // 🔧 存储当前风险交易数据供全局函数使用
        window.currentRiskTransactions = transactions;

        if (!transactions || !transactions.length) {
            console.warn('❌ 没有交易数据，显示无数据消息');
            container.innerHTML = '<div class="no-data">暂无风险交易记录</div>';
            return;
        }

        console.log('✅ 准备显示', transactions.length, '条交易记录');

        const html = transactions.slice(0, 20).map((tx, index) => `
            <div class="risk-transaction-item">
                <div class="tx-id">${tx.member_id || tx.transaction_id || 'N/A'}</div>
                <div class="risk-type">${this.formatRiskType(tx.detection_type || tx.risk_type)}</div>
                <div class="tx-volume">${window.formatNumber(tx.volume || tx.transaction_volume || 0)}</div>
                <div class="detection-method">${tx.detection_type || tx.detection_method || 'N/A'}</div>
                <div class="tx-time">${window.formatTime(tx.timestamp || tx.transaction_time)}</div>
                <div class="tx-actions">
                    <button class="btn btn-sm btn-outline-info" onclick="showTransactionDetails(${index}, '${tx.detection_type}')">
                        <i class="bi bi-info-circle"></i> 详情
                    </button>
                </div>
            </div>
        `).join('');
        
        container.innerHTML = html;
    }

    /**
     * 按类型显示风险交易统计
     */
    displayRiskTransactionsByType(riskByType) {
        const container = document.getElementById('riskTransactionsByType');
        if (!container) return;

        const types = Object.keys(riskByType);
        if (!types.length) {
            container.innerHTML = '<div class="no-data">暂无风险交易统计</div>';
            return;
        }

        const html = types.map(type => {
            const transactions = riskByType[type];
            return `
                <div class="risk-type-stat">
                    <div class="type-name">${this.formatRiskType(type)}</div>
                    <div class="type-count">${transactions.length}笔</div>
                    <div class="type-volume">${window.formatNumber(transactions.reduce((sum, tx) => sum + tx.transaction_volume, 0))}</div>
                </div>
            `;
        }).join('');
        
        container.innerHTML = html;
    }

    // ================================
    // 辅助函数
    // ================================

    /**
     * 更新元素文本内容
     */
    updateElementText(elementId, text) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = text;
        } else {
            // 元素不存在（可能已被移除）
        }
    }

    // 删除重复的updateProgressBar方法，使用utils.js中的统一实现

    // 删除重复的updateScoreBar方法，使用utils.js中的统一实现

    // 删除重复的getScoreColor方法，使用utils.js中的getScoreLevelClass

    // 删除重复的formatNumber方法，使用utils.js中的统一实现

    /**
     * 格式化风险类型
     */
    formatRiskType(riskType) {
        const riskTypeMapping = {
            'high_frequency_trading': '高频交易',
            'wash_trading': '对敲交易',
            'suspected_wash_trading': '疑似对敲交易',
            'large_volume_trading': '大额交易',
            'abnormal_profit': '异常盈利',
            'market_manipulation': '市场操纵',
            'abnormal_trading_pattern': '异常交易模式',
            'cross_platform_arbitrage': '跨平台套利',
            'unknown': '未知风险'
        };
        
        return riskTypeMapping[riskType] || riskType;
    }

    /**
     * 🔧 废弃：计算评分 - 注释掉前端计算，保留方法结构
     * 注释原因：前端不再进行评分计算，改为使用后端预计算的评分数据
     */


    /**
     * 获取一致性描述
     */
    getConsistencyDescription(consistency) {
        if (consistency >= 0.8) return '高度一致';
        if (consistency >= 0.6) return '较为一致';
        if (consistency >= 0.4) return '中等一致';
        if (consistency >= 0.2) return '较不一致';
        return '不一致';
    }

    // 显示方法已移至专门的显示组件中
    
    /**
     * 标签切换方法
     */
    switchAssociationTab(tabName) {
        this.riskAnalysisDisplay.switchAssociationTab(tabName);
    }
    
    switchBehaviorTab(tabName) {
        this.userBehaviorDisplay.switchBehaviorTab(tabName);
    }
    
    switchMetricTab(tabName) {
        this.userBehaviorDisplay.switchMetricTab(tabName);
    }

    /**
     * 🚀 新增：格式化 peak_trading_hours 数据为文本
     */
    formatPeakTradingHours(peakHoursData) {
        try {
            if (!peakHoursData) {
                return null;
            }

            let hourlyData = {};

            // 解析数据
            if (typeof peakHoursData === 'string') {
                hourlyData = JSON.parse(peakHoursData);
            } else if (typeof peakHoursData === 'object') {
                hourlyData = peakHoursData;
            } else {
                return null;
            }

            // 找出交易次数最多的时段
            const sortedHours = Object.entries(hourlyData)
                .map(([hour, count]) => ({ hour: parseInt(hour), count: parseInt(count || 0) }))
                .filter(item => item.count > 0)
                .sort((a, b) => b.count - a.count);

            if (sortedHours.length === 0) {
                return '暂无交易数据';
            }

            // 取前3个最活跃时段
            const topHours = sortedHours.slice(0, 3);
            const peakHoursText = topHours.map(item => {
                const startHour = item.hour.toString().padStart(2, '0');
                const endHour = ((item.hour + 1) % 24).toString().padStart(2, '0');
                return `${startHour}:00-${endHour}:00`;
            }).join(', ');

            return peakHoursText;

        } catch (e) {
            console.error('格式化 peak_trading_hours 数据失败:', e);
            return '数据解析失败';
        }
    }

    /**
     * 关闭所有模态框
     */
    closeAllModals() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.style.display = 'none';
        });
    }
}

/**
 * 应用状态管理类
 */
class AppState {
    constructor() {
        this.currentUserData = null;
        this.currentBehaviorData = null;
        this.currentPage = 1;
        this.pageSize = 10;
        this.filteredTransactions = [];
        this.riskCategoriesChart = null;
    }
    
    reset() {
        this.currentUserData = null;
        this.currentBehaviorData = null;
        this.currentPage = 1;
        this.filteredTransactions = [];
        if (this.riskCategoriesChart) {
            this.riskCategoriesChart.destroy();
            this.riskCategoriesChart = null;
        }
    }
}

// 全局应用实例
let userAnalysisApp = null;

// 页面初始化
document.addEventListener('DOMContentLoaded', async function() {
    userAnalysisApp = new UserAnalysisApp();
    await userAnalysisApp.init();
    
    // 全局暴露应用实例，保持向后兼容
    window.userAnalysisApp = userAnalysisApp;
    
    // 暴露一些全局函数以保持向后兼容
    
    window.copyToClipboard = copyToClipboard;
    window.showToast = showToast;

    // 🔧 新增：全局交易详情显示函数
    window.showTransactionDetails = function(index, detectionType) {
        console.log('显示交易详情:', index, detectionType);

        // 获取当前显示的风险交易数据
        if (!window.currentRiskTransactions) {
            console.error('未找到风险交易数据');
            return;
        }

        const transaction = window.currentRiskTransactions[index];
        if (!transaction) {
            console.error('未找到指定的交易记录:', index);
            return;
        }

        // 根据检测类型决定显示方式
        if (detectionType && detectionType.includes('wash')) {
            // 对敲交易，显示详细的交易对信息
            if (typeof showWashTradingDetailsModal === 'function') {
                showWashTradingDetailsModal(transaction);
            } else {
                console.error('showWashTradingDetailsModal函数未定义');
                alert('对敲交易详情功能暂不可用');
            }
        } else {
            // 其他类型交易，显示简单信息
            if (typeof displaySimpleTransactionModal === 'function') {
                displaySimpleTransactionModal(transaction);
            } else {
                console.error('displaySimpleTransactionModal函数未定义');
                alert('交易详情功能暂不可用');
            }
        }
    };
});

export default UserAnalysisApp;